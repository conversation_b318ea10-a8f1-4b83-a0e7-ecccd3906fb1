"use client"

import { useSidebarStore } from "@/stores/sidebarStore"
import { LeftSidebar } from "./LeftSidebar"
import { MainCanvas } from "./MainCanvas"
import { OutermostSidebar } from "./OutermostSidebar"
import { RightSidebar } from "./RightSidebar"
import { SidebarToggleButton } from "./SidebarToggleButton"
import { TopToolbar } from "./TopToolbar"

export function EditorLayout() {
  const { isCollapsed } = useSidebarStore()
  return (
    <div className="h-full w-full flex flex-col bg-gradient-to-br from-background via-background to-muted/5">
      {/* Top Toolbar */}
      <TopToolbar />

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden relative">
        {/* Outermost Sidebar - Fixed positioned, overlays on top */}
        <OutermostSidebar />

        {/* Tools/Blocks Sidebar - Fixed positioned like sim-studio */}
        <LeftSidebar />

        {/* Content area with static layout - no dynamic padding to prevent shifting */}
        <div className="flex-1 flex">
          {/* Main Canvas */}
          <div className="flex-1 relative bg-dots z-10">
            <MainCanvas />
          </div>

          {/* Right Sidebar */}
          <RightSidebar />
        </div>

        {/* Sidebar Toggle Button - Shows when sidebar is collapsed */}
        <SidebarToggleButton />
      </div>
    </div>
  )
}
